{"name": "shop-service", "version": "0.1.0", "description": "weixin mini programe shop services", "author": "", "license": "MIT", "scripts": {"typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "build": "tsc -p tsconfig.json", "start-ts": "ts-node -r tsconfig-paths/register src/main.ts", "start": "node -r dotenv/config dist/main.js", "dev": "tsc-watch --on<PERSON><PERSON><PERSON> \"nodemon\"", "test": "tsc-watch --onSuccess \"nodemon --config nodemon-test.json\"", "debug": "nodemon --config nodemon-debug.json"}, "dependencies": {"@chankamlam/nest-jaeger": "^1.0.16", "@nestjs/common": "^7.4.2", "@nestjs/core": "^7.4.2", "@nestjs/platform-express": "^7.4.2", "@nestjs/typeorm": "^6.1.2", "axios": "^0.19.2", "axios-opentracing": "^0.1.3", "class-transformer": "^0.2.3", "class-validator": "^0.9.1", "date-fns": "^1.30.1", "dotenv": "^8.2.0", "fs": "0.0.1-security", "jaeger-client": "^3.18.1", "lodash": "^4.17.11", "module-alias": "^2.2.0", "mysql": "^2.17.1", "opentracing": "^0.14.5", "qs": "^6.9.1", "redis": "^3.0.2", "rxjs": "^6.5.4", "source-map-support": "^0.5.16", "typeorm": "^0.2.18", "uuid": "^3.3.2"}, "devDependencies": {"@nestjs/testing": "^7.4.2", "@types/express": "^4.16.0", "@types/form-data": "^2.5.0", "@types/lodash": "^4.14.149", "@types/md5": "^2.1.33", "@types/node": "^10.12.18", "@types/qs": "^6.9.0", "@types/redis": "^2.8.15", "nodemon": "^1.19.4", "ts-node": "8.1.0", "tsc-watch": "^4.1.0", "tsconfig-paths": "3.8.0", "typescript": "3.7.4"}, "_moduleAliases": {"@src": "./dist"}}