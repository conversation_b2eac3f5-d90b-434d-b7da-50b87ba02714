# config start
YQZ_SERVICE_NAME=shopService
YQZ_TARGET_ENV=dev
YQZ_SERVICE_PORT=3000

# api
YQZ_BINGOHOME_API=https://cs.1qizhuang.com/bingohome
YQZ_MP_PLATFORM_API=https://cs.1qizhuang.com/api/mp-platform
WEIXIN_API=https://api.weixin.qq.com

#databasesettings
TYPEORM_CONNECTION=mysql
TYPEORM_HOST=rds.timontech.cn
TYPEORM_USERNAME=yqz_mp_platform
TYPEORM_PASSWORD=98bb4KJT7hM4vE22mrtq(u*7mKJroHrw
TYPEORM_DATABASE=mp_platform-dev
TYPEORM_PORT=3306
TYPEORM_SYNCHRONIZE=false
TYPEORM_LOGGING=false
TYPEORM_ENTITIES=dist/**/*.entity.js

#tracer
TRACER_SERVICE_NAME=shop-service-dev
COLLECTOR_END_POINT=http://tracing-analysis-dc-sh.aliyuncs.com/adapt_c6a30eb76x@58ab9008a817119_c6a30eb76x@53df7ad2afe8301/api/traces
# config end