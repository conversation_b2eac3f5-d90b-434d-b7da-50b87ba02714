export type WxShopProductInfo = {
	title: string;
	sub_title: string;
	head_img: string[];
	desc_info: {
		imgs: string[];
	};
	out_product_id: string;
	product_id: number;
	brand_id: number;                   
	status: WXProductStatus;            // to-do-elicxh
	edit_status: WxProductEditStatus;   // to-do-elicxh
	min_price: number;
	path: string;
	cats: {
		cat_id: number;
		level: number;
	}[];
	attrs: {
		attr_key: string;
		attr_value: string;
	}[];
	model: string;
	shopcat: {
		shopcat_id: number;
	}[];
	skus: {
		sku_id: number;
	}[];
};

// https://developers.weixin.qq.com/doc/ministore/minishopopencomponent/API/spu/get_spu_list.html
//  0	初始值
//  1	编辑中
//  2	审核中
//  3	审核失败
//  4	审核成功
export enum WxProductEditStatus {
    INIT,
    EDITING,
    AUDITING,
    FAIL,
    SUCCESS
}

// 0	初始值
// 5	上架
// 6	回收站
// 9	逻辑删除
// 11	自主下架
// 12	售磬下架
// 13	违规下架/风控系统下架
export enum WXProductStatus {
    INIT = 0,
    ON_SALE = 5,
    RECYCLE = 6,
    LOGIC_DELETE = 9,
    OFF_SALE_SELF = 11,
    OFF_SALE_SOLD_NONE = 12,
    VIOLATION = 13
}