import { TypeOrmModule } from "@nestjs/typeorm";
import { Module } from "@nestjs/common";

import { MyLoggerModule } from "./my-logger/my-logger.module";
import { AppController } from "./app.controller";
import { AppService } from "./service/app.service";
import { MpShopDao } from "./service/dao/mp-shop.dao";



@Module({
  imports: [TypeOrmModule.forRoot(), MyLoggerModule],
  controllers: [AppController],
  providers: [AppService,MpShopDao],
})
export class AppModule {}
