import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { AppService } from './service/app.service';
import { YqzException } from './filters/yqz-exception.filter';
import { ResponseInterceptor } from './interceptor/response.interceptor';

@Controller('')
@UseInterceptors(ResponseInterceptor)
export class AppController {
	constructor(private readonly appService: AppService) {}

	/**
   * @description 根据条件获取“微信小商店”商品列表;支持搜索
   * @param appId        小程序的appId
   * @param pageNo       分页 pageNo
   * @param pageSize     分页 pageSize
   * @param keyword [?]  搜索关键字
   * @returns { list: WxShopProductInfo[]}
   *     
   * "status": 5,  默认值
   * "need_edit_spu": 0  默认0:获取线上数据, 1:获取草稿数据
  */
	@Post('product/list')
	async productList(@Body() body: { appId: string; pageNo: number; pageSize: number; keyword?: string }) {
		const { appId, pageNo, pageSize, keyword } = body;

		if (!appId) throw new YqzException(1, '请输入appId!');
		if (!pageNo) throw new YqzException(1, '请输入pageNo!');
		if (!pageSize) throw new YqzException(1, '请输入pageSize!');
		
		const list = keyword
		? await this.appService.searchWxMpProductList({ appId, pageNo, pageSize, keyword })
		: await this.appService.getWxMpProductList({ appId, pageNo, pageSize });

		return {
			list
		}
	}

	/**
   * @description 获取“微信小商店”商店信息
   * @param companyId
   * @param appId 
   * @returns {appId:string, companyId:string,  open: "Y" | "N"}
   */
	@Post('info')
	info(@Body() body: { appId?: string; companyId?: string }): any {
		if (!body.appId && !body.companyId) throw new YqzException(1, '请输入appId或companyId');
		const res = this.appService.getMpProductInfo({
			appId: body.appId,
			companyId: body.companyId
		});
		return res;
	}
}
