import axios, { AxiosResponse }  from "axios";
import { MyLogger } from "@src/my-logger/my-logger.service";
import { tracerConfig, tracerOptions } from "@src/config/tracer.config";
const { initTracer } = require('jaeger-client');
const createAxiosTracing = require('axios-opentracing').default;
var apiClient = axios.create();

const myLogger = new MyLogger('BingohomeClient');
const UNIQUE_KEY_FIELD='_yqz_key';

// 设置默认参数
apiClient.defaults.baseURL = process.env.YQZ_BINGOHOME_API;
apiClient.defaults.headers.post['Content-Type'] = 'application/json';

apiClient.interceptors.request.use(function (config) {
    const uid = new Date().getTime();
    config.headers[UNIQUE_KEY_FIELD]=uid;
    myLogger.log({
      requestId:uid,
      method: config.method,
      data: config.data,
      params: config.params
    })

    return config;
  }, function (error) {
    return Promise.reject(error);
  });

apiClient.interceptors.response.use(function (response) {
  const uid = response.config.headers[UNIQUE_KEY_FIELD];
  myLogger.log({
    responseId:uid,
    data: JSON.stringify(response.data)
  })
    return response;
  }, function (error) {
    return Promise.reject(error);
});

// Setup tracer
const tracer = initTracer(tracerConfig,tracerOptions);
// Create tracing applyer
const applyTracingInterceptors = createAxiosTracing(tracer);
// Create root span
const rootSpan = tracer.startSpan(process.env.TRACER_SERVICE_NAME);
applyTracingInterceptors(apiClient, { span: rootSpan });
rootSpan.finish();

export {
    apiClient as BingohomeClient
}


