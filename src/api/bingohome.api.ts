
import {BingohomeClient} from "./api-client/bingohome-api-client";
export {
    getCompanyDetail
}

async function getCompanyDetail(companyId:number):Promise<CompanyDetail>{
    const res = await BingohomeClient.get<CompanyDetail>('getCompanyDetail.do', {
        params: {
            companyId
        }
    });
    return res.data;
}


type CompanyDetail = {
    companyId:number;
    companyName:string;
}