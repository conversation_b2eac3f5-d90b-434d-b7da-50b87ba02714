import { WxShopProductInfo } from '@src/type/wx-response.type';
import { WeixinApiClient as wxClient } from './api-client/wx-api-client';

export { getProductList, searchProductList };

// 根据accessToken获取微信小商店的产品列表
async function getProductList(params: {
	accessToken: string;
	pageNo: number;
	pageSize: number;
	status?: number;
	needEditSpu?: number;
}): Promise<{
	errcode: number;
	errmsg: string;
	spus: WxShopProductInfo[];
}> {
	const { accessToken, pageNo, pageSize, status, needEditSpu } = params;
	const url = `product/spu/get_list?access_token=${accessToken}`;
	const res = await wxClient.post(url, {
		page: pageNo,
		page_size: pageSize,
		status: status || 5, // 5 上架
		need_edit_spu: needEditSpu || 0 // 默认0:获取线上数据, 1:获取草稿数据
	});
	return res.data;
}

// 根据accessToken,keyword 搜索微信小商店的产品列表
async function searchProductList(params: {
	accessToken: string;
	pageNo: number;
    pageSize: number;
    keyword: string;
}): Promise<{
	errcode: number;
	errmsg: string;
	spus: WxShopProductInfo[];
}> {
	const { accessToken, pageNo, pageSize,keyword } = params;
	const url = `product/spu/search?access_token=${accessToken}`;
	console.log("elicxh test keyword",keyword,pageNo,pageSize)
	const res = await wxClient.post(url, {
		keyword: keyword,
		page: pageNo,
		page_size: pageSize
	});
	console.log('elicxh test res',res.data)
	console.log('elicxh test res',res)	
	return res.data;
}
