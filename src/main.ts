require("module-alias/register"); /* tslint:disable-line */
require('source-map-support').install();

import * as _ from "lodash";
import {JaegerInterceptor} from '@chankamlam/nest-jaeger'
console.log('===================================')
_.keys(process.env).forEach( key => {
  if(key.indexOf('YQZ_')>=0 || key.indexOf('TYPEORM_')>=0) {
    console.log(`${key}: ${process.env[key]}`);
  }
})
console.log('===================================')


import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { ValidationPipe } from "@nestjs/common";
import { MyLogger } from "./my-logger/my-logger.service";
import { LoggingInterceptor } from "./interceptor/logging.interceptor";
import { AllExceptionsFilter } from "./filters/yqz-exception.filter";
import { tracerConfig, tracerOptions } from "./config/tracer.config";

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger:false // use custom logger MyLoggeer
  });


  app.useGlobalPipes(new ValidationPipe());
  app.useLogger(app.get(MyLogger))
  app.useGlobalInterceptors(new LoggingInterceptor());  
  app.useGlobalFilters(new AllExceptionsFilter());
  app.useGlobalInterceptors(new JaegerInterceptor(tracerConfig, tracerOptions));

  await app.listen(process.env.YQZ_SERVICE_PORT);
	console.log(`${bootstrap.name} listening on port ${process.env.YQZ_SERVICE_PORT} ...`);

}

bootstrap();