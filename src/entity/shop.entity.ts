import { Column, <PERSON><PERSON>ty, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";
import { Base } from "./base.entity";
// to-do-elicxh

@Entity("mp_shop")
export class ShopEntity extends Base { 
    @PrimaryGeneratedColumn()
    id: string;

    @Column({name: "app_id"})
    appId: string;

    @Column({name: "company_id"})
    companyId: string;

    @Column({name: "open"})
    open: string;
}