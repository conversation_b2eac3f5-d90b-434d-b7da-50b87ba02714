import {Column, CreateDateColumn, UpdateDateColumn, BeforeInsert, BeforeUpdate} from "typeorm";


export abstract class Base {

    @Column({name: "delete_flag", select:false})
    deleteFlag: string;

    @Column({ nullable: false, select:false})
    createTime: Date;

    @Column({select:false})
    updateTime: Date;

    @Column({select:false})
    createBy: string;

    @Column({select:false})
    updateBy: string;

    @BeforeInsert()
    beforeInsert() {
        this.createTime = new Date();
        this.updateTime = this.updateTime || new Date();
    }

    @BeforeUpdate()
    beforeUpdate() {
        this.updateTime = new Date();
    }
}
