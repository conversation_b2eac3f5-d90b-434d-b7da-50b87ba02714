import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger, HttpStatus, NotFoundException } from '@nestjs/common';
import { MyLogger } from '@src/my-logger/my-logger.service';
import * as util from 'util' // has no default export

export class YqzException  {
  yqzErrorCode:number;
  yqzErrorMessage:string;
  static DEFAULT_ERROR_CODE_1=1;
  static DEFAULT_ERROR_CODE_2=2;
  static CUSTOM_ERROR_CODE=3;
  constructor(errorCode, errorMessage) {
    this.yqzErrorCode = errorCode;
    this.yqzErrorMessage = errorMessage
  }
}



@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private logger:MyLogger = new MyLogger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    
    if(exception instanceof  NotFoundException) return;

    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    
    const rid = new Date().getTime();
    this.logger.error({rid, exception});
    
    if(exception instanceof YqzException) {
      response.status(200).json({
        error_code: exception.yqzErrorCode,
        error_msg: exception.yqzErrorMessage,
        rid
      });
      return;
    }
    
    if(exception instanceof  Error) {
      response.status(200).json({
        error_code: YqzException.DEFAULT_ERROR_CODE_1,
        error_msg: exception.message,
        rid
      });
      return;
    }
    
    response.status(200).json({
      error_code: YqzException.DEFAULT_ERROR_CODE_2,
      error_msg: "系统异常",
      rid
    });
    
   
  }
}
