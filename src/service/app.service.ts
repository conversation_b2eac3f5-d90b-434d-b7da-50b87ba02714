import { Injectable } from '@nestjs/common';
import { BingohomeApi } from '../api';
import { getMpAccessToken } from '../api/mp-platform.api';
import { getProductList, searchProductList } from '../api/wx.api';
import { YqzException } from '../filters/yqz-exception.filter';
import { MyLogger } from '../my-logger/my-logger.service';
import { WxShopProductInfo } from '../type/wx-response.type';
import { MpShopDao } from './dao/mp-shop.dao';

@Injectable()
export class AppService {
	private logger = new MyLogger(AppService.name);

	constructor(private readonly mpShopDao: MpShopDao) {}

	async getWxMpProductList(params: {
		appId: string;
		pageNo: number;
		pageSize: number;
	}): Promise<WxShopProductInfo[]> {
		const { accessToken } = await getMpAccessToken({ appId: params.appId });

		if (!accessToken) throw new YqzException(1, '获取accessToken失败');

		const res = await getProductList({
			accessToken,
			pageNo: params.pageNo,
			pageSize: params.pageSize
		});

		if (!res) throw new YqzException(1, '获取微信失败productList');
		if (res.errcode !== 0)
			throw new YqzException(1, `获取微信失败productList errcode:${res.errcode} errmsg:${res.errmsg}`);
		return res.spus;
	}

	async searchWxMpProductList(params: {
		appId: string;
		pageNo: number;
		pageSize: number;
		keyword: string;
	}): Promise<WxShopProductInfo[]> {
		const { accessToken } = await getMpAccessToken({ appId: params.appId });

		if (!accessToken) throw new YqzException(1, '获取accessToken失败');

		const res = await searchProductList({
			accessToken,
			pageNo: params.pageNo,
			pageSize: params.pageSize,
			keyword: params.keyword
		});

		if (!res) throw new YqzException(1, '获取微信失败productList');
		return res.spus || [];
	}

	async getMpProductInfo(params: {appId: string,companyId: string}){
		return await this.mpShopDao.searchMpShop(params)
	}
}
