import { Injectable } from '@nestjs/common';
import { Connection, FindConditions } from 'typeorm';
import { MyLogger } from '@src/my-logger/my-logger.service';
import { ShopEntity } from '@src/entity/shop.entity';

@Injectable()
export class MpShopDao {
	private readonly myLogger: MyLogger = new MyLogger(MpShopDao.name);

	constructor(private connection: Connection) {}

	async searchMpShop(params: { appId?: string; companyId?: string; open?: string }) {
		let where: FindConditions<ShopEntity> = { deleteFlag: 'N' };
		if (params.appId) where.appId = params.appId;
		if (params.companyId) where.companyId = params.companyId;
		if (params.open) where.open = params.open;

		return await this.connection.manager.findOne(ShopEntity, where);
	}
}
