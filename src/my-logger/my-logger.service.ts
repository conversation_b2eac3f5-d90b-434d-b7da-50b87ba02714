import { Logger } from '@nestjs/common';

export class MyLogger extends Logger {
  private myContext: string[];

  constructor(context?: string, isTimestampEnabled?: boolean) {
    console.log("init logger", context || "App");
    super(context, isTimestampEnabled);
    this.myContext = context? [context] : [];
  }

  pushContext(subContext:string) {
    this.myContext.push(subContext);
  }
  popContext() {
    this.myContext.pop();
  }

  error(message:any) {    
    console.error(this.myContext.join(' '), ...arguments);
  }

  debug(message:string, trace:string) {
    console.debug(this.myContext.join(' '), ...arguments);
  }

  log(message:any) {
    console.log(this.myContext.join(' '), ...arguments);
  }
  warn(){
    console.warn(this.myContext.join(' '), ...arguments);
  }

  verbose() {
    // comment below to disable verbose
    // super.verbose(message, context);
  }



  


  
}
