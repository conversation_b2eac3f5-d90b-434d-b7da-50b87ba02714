import { Injectable, NestInterceptor, Execution<PERSON>ontext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
// import { tap } from 'rxjs/operators';
import { map } from 'rxjs/operators';
import * as util from "util";
import * as _ from "lodash";

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    
    const excludePaths = ['/'];
    if(excludePaths.includes(request.url)) return next.handle();
    
    const now = Date.now();
    
    if(request.method=="POST") {
      const message = `${request.method} ${now} request ${request.url} ${JSON.stringify(request.body)}`;
      console.log(message);    
    } else {
      const message = `${request.method} ${now} request ${request.url} ${JSON.stringify(request.query)}`;
      console.log(message);    
    }
    
    
    
    return next.handle().pipe(map(data => {
      let newData = processResponseData(data);
      let logstr = JSON.stringify(newData).substr(0,799);
      const message = `${request.method} ${now} response ${logstr} , toke ${new Date().getTime()-now}ms`
      console.log(message);                     
      return newData;
    }));
  }
}

function processResponseData(data:any) {
  if(!data) return data;
  if('object'!==typeof data) return data;
  if(_.isArray(data)) return data;
  return {    
    rid: new Date().getTime(),
    ...data

  }
}